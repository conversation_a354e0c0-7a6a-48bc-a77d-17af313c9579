export const translations = {
  en: {
    appName: "TypingTest",
    testDuration: "Test Duration:",
    startTest: "Start Test",
    testing: "Testing...",
    reset: "Reset",
    wpm: "WPM",
    accuracy: "Accuracy",
    timeLeft: "Time Left",
    errors: "Errors",
    typeTextAbove: "Type the text above",
    clickToStart: "Click here or press any key to start typing",
    correct: "Correct",
    incorrect: "Incorrect",
    current: "Current",
    testComplete: "Test Complete!",
    resultsDescription: "Here are your typing test results",
    wordsPerMinute: "Words Per Minute",
    average: "Average",
    target: "Target",
    timeTaken: "Time Taken",
    testDurationLabel: "Test Duration",
    totalErrors: "Total Errors",
    incorrectKeystrokes: "Incorrect keystrokes",
    takeAnotherTest: "Take Another Test",
    shareResults: "Share Results",
    saveResults: "Save Results",
    saving: "Saving...",
    virtualKeyboard: "Virtual Keyboard",
    show: "Show",
    hide: "Hide",
    footerText: "Improve your typing skills with our free typing tests.",
    privacyPolicy: "Privacy Policy",
    termsOfService: "Terms of Service",
    backToApp: "Back to TypingTest",
    lastUpdated: "Last updated:",
    informationWeCollect: "Information We Collect",
    howWeUseInfo: "How We Use Your Information",
    dataStorage: "Data Storage",
    thirdPartyServices: "Third-Party Services",
    dataSecurity: "Data Security",
    contactUs: "Contact Us",
    acceptanceOfTerms: "Acceptance of Terms",
    useLicense: "Use License",
    serviceAvailability: "Service Availability",
    userConduct: "User Conduct",
    privacy: "Privacy",
    limitations: "Limitations",
    modifications: "Modifications",
    contactInformation: "Contact Information",
    language: "Language",
    difficulty: "Difficulty",
    beginner: "Beginner",
    intermediate: "Intermediate",
    advanced: "Advanced",
    // Terms of Service content
    acceptanceOfTermsContent: "By accessing and using TypingTest, you accept and agree to be bound by the terms and provision of this agreement.",
    useLicenseContent: "Permission is granted to temporarily access TypingTest for personal, non-commercial use only. This is the grant of a license, not a transfer of title, and under this license you may not:",
    useLicenseItem1: "Modify or copy the materials",
    useLicenseItem2: "Use the materials for any commercial purpose or for any public display",
    useLicenseItem3: "Attempt to reverse engineer any software contained on the website",
    useLicenseItem4: "Remove any copyright or other proprietary notations from the materials",
    serviceAvailabilityContent: "TypingTest is provided \"as is\" without any representations or warranties. We do not guarantee that the service will be uninterrupted, timely, secure, or error-free.",
    userConductContent: "You agree to use TypingTest only for lawful purposes and in a way that does not infringe the rights of others or restrict their use and enjoyment of the service.",
    privacyContent: "Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the service, to understand our practices.",
    limitationsContent: "In no event shall TypingTest or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use TypingTest.",
    modificationsContent: "TypingTest may revise these terms of service at any time without notice. By using this service, you are agreeing to be bound by the then current version of these terms of service.",
    contactInformationContent: "If you have any questions about these Terms of Service, please contact us through our website.",
    privacyContent1: "We collect information you provide directly to us when using our typing test service:",
    privacyList1: "Test results including words per minute (WPM), accuracy, and errors",
    privacyList2: "Language and difficulty preferences",
    privacyList3: "Test duration and completion times",
    howWeUseContent: "We use the information we collect to:",
    useList1: "Provide and improve our typing test service",
    useList2: "Display your test results and progress",
    useList3: "Analyze usage patterns to enhance user experience",
    useList4: "Generate aggregate statistics about typing performance",
    dataStorageContent: "Your test results are stored locally in your browser and temporarily on our servers. We do not permanently store personal identifying information unless you explicitly provide it.",
    thirdPartyContent: "We may use third-party analytics services to understand how our service is used. These services may collect information about your usage patterns in accordance with their own privacy policies.",
    dataSecurityContent: "We implement appropriate technical and organizational measures to protect your information against unauthorized access, alteration, disclosure, or destruction.",
    contactUsContent: "If you have any questions about this Privacy Policy, please contact us through our website."
  },
  zh: {
    appName: "打字测试",
    testDuration: "测试时长：",
    startTest: "开始测试",
    testing: "测试中...",
    reset: "重置",
    wpm: "字/分",
    accuracy: "准确率",
    timeLeft: "剩余时间",
    errors: "错误",
    typeTextAbove: "请输入上方文字",
    clickToStart: "点击此处或按任意键开始打字",
    correct: "正确",
    incorrect: "错误",
    current: "当前",
    testComplete: "测试完成！",
    resultsDescription: "这是您的打字测试结果",
    wordsPerMinute: "每分钟字数",
    average: "平均",
    target: "目标",
    timeTaken: "用时",
    testDurationLabel: "测试时长",
    totalErrors: "总错误数",
    incorrectKeystrokes: "错误击键",
    takeAnotherTest: "再次测试",
    shareResults: "分享结果",
    saveResults: "保存结果",
    saving: "保存中...",
    virtualKeyboard: "虚拟键盘",
    show: "显示",
    hide: "隐藏",
    footerText: "通过我们的免费打字测试提高您的打字技能。",
    privacyPolicy: "隐私政策",
    termsOfService: "服务条款",
    backToApp: "返回打字测试",
    lastUpdated: "最后更新：",
    informationWeCollect: "我们收集的信息",
    howWeUseInfo: "我们如何使用您的信息",
    dataStorage: "数据存储",
    thirdPartyServices: "第三方服务",
    dataSecurity: "数据安全",
    contactUs: "联系我们",
    acceptanceOfTerms: "条款接受",
    useLicense: "使用许可",
    serviceAvailability: "服务可用性",
    userConduct: "用户行为",
    privacy: "隐私",
    limitations: "限制",
    modifications: "修改",
    contactInformation: "联系信息",
    language: "语言",
    difficulty: "难度",
    beginner: "初级",
    intermediate: "中级",
    advanced: "高级",
    // Terms of Service content
    acceptanceOfTermsContent: "通过访问和使用打字测试，您接受并同意受本协议条款和条件的约束。",
    useLicenseContent: "仅授予您临时访问打字测试进行个人、非商业用途的许可。这是许可的授予，而非所有权的转让，在此许可下您不得：",
    useLicenseItem1: "修改或复制材料",
    useLicenseItem2: "将材料用于任何商业目的或任何公开展示",
    useLicenseItem3: "尝试对网站上包含的任何软件进行逆向工程",
    useLicenseItem4: "从材料中删除任何版权或其他专有标记",
    serviceAvailabilityContent: "打字测试按\"现状\"提供，不提供任何陈述或保证。我们不保证服务将不间断、及时、安全或无错误。",
    userConductContent: "您同意仅将打字测试用于合法目的，并且不侵犯他人权利或限制他们对服务的使用和享受。",
    privacyContent: "您的隐私对我们很重要。请查看我们的隐私政策，该政策也管理您对服务的使用，以了解我们的做法。",
    limitationsContent: "在任何情况下，打字测试或其供应商均不对因使用或无法使用打字测试而产生的任何损害（包括但不限于数据丢失或利润损失或业务中断造成的损害）承担责任。",
    modificationsContent: "打字测试可能随时修订这些服务条款，恕不另行通知。通过使用此服务，您同意受当时现行版本的服务条款约束。",
    contactInformationContent: "如果您对这些服务条款有任何疑问，请通过我们的网站联系我们。",
    privacyContent1: "我们收集您在使用我们的打字测试服务时直接提供给我们的信息：",
    privacyList1: "测试结果，包括每分钟字数(WPM)、准确性和错误",
    privacyList2: "语言和难度偏好",
    privacyList3: "测试持续时间和完成时间",
    howWeUseContent: "我们使用收集的信息来：",
    useList1: "提供和改进我们的打字测试服务",
    useList2: "显示您的测试结果和进度",
    useList3: "分析使用模式以增强用户体验",
    useList4: "生成关于打字性能的汇总统计",
    dataStorageContent: "您的测试结果存储在您的浏览器本地和我们服务器的临时存储中。除非您明确提供，否则我们不会永久存储个人识别信息。",
    thirdPartyContent: "我们可能使用第三方分析服务来了解我们的服务如何被使用。这些服务可能根据其自己的隐私政策收集有关您使用模式的信息。",
    dataSecurityContent: "我们实施适当的技术和组织措施来保护您的信息免受未经授权的访问、更改、披露或破坏。",
    contactUsContent: "如果您对此隐私政策有任何问题，请通过我们的网站联系我们。"
  },
  es: {
    appName: "Prueba de Mecanografía",
    testDuration: "Duración de la prueba:",
    startTest: "Iniciar Prueba",
    testing: "Probando...",
    reset: "Reiniciar",
    wpm: "PPM",
    accuracy: "Precisión",
    timeLeft: "Tiempo Restante",
    errors: "Errores",
    typeTextAbove: "Escribe el texto de arriba",
    clickToStart: "Haz clic aquí o presiona cualquier tecla para empezar a escribir",
    correct: "Correcto",
    incorrect: "Incorrecto",
    current: "Actual",
    testComplete: "¡Prueba Completada!",
    resultsDescription: "Aquí están los resultados de tu prueba de mecanografía",
    wordsPerMinute: "Palabras Por Minuto",
    average: "Promedio",
    target: "Objetivo",
    timeTaken: "Tiempo Tomado",
    testDurationLabel: "Duración de la Prueba",
    totalErrors: "Errores Totales",
    incorrectKeystrokes: "Pulsaciones incorrectas",
    takeAnotherTest: "Hacer Otra Prueba",
    shareResults: "Compartir Resultados",
    saveResults: "Guardar Resultados",
    saving: "Guardando...",
    virtualKeyboard: "Teclado Virtual",
    show: "Mostrar",
    hide: "Ocultar",
    footerText: "Mejora tus habilidades de mecanografía con nuestras pruebas gratuitas.",
    privacyPolicy: "Política de Privacidad",
    termsOfService: "Términos de Servicio",
    backToApp: "Volver a Prueba de Mecanografía",
    lastUpdated: "Última actualización:",
    informationWeCollect: "Información que Recopilamos",
    howWeUseInfo: "Cómo Usamos Su Información",
    dataStorage: "Almacenamiento de Datos",
    thirdPartyServices: "Servicios de Terceros",
    dataSecurity: "Seguridad de Datos",
    contactUs: "Contáctanos",
    acceptanceOfTerms: "Aceptación de Términos",
    useLicense: "Licencia de Uso",
    serviceAvailability: "Disponibilidad del Servicio",
    userConduct: "Conducta del Usuario",
    privacy: "Privacidad",
    limitations: "Limitaciones",
    modifications: "Modificaciones",
    contactInformation: "Información de Contacto",
    language: "Idioma",
    difficulty: "Dificultad",
    beginner: "Principiante",
    intermediate: "Intermedio",
    advanced: "Avanzado",
    // Terms of Service content
    acceptanceOfTermsContent: "Al acceder y usar TypingTest, usted acepta y acuerda estar sujeto a los términos y disposiciones de este acuerdo.",
    useLicenseContent: "Se otorga permiso para acceder temporalmente a TypingTest solo para uso personal y no comercial. Esta es la concesión de una licencia, no una transferencia de título, y bajo esta licencia usted no puede:",
    useLicenseItem1: "Modificar o copiar los materiales",
    useLicenseItem2: "Usar los materiales para cualquier propósito comercial o para cualquier exhibición pública",
    useLicenseItem3: "Intentar realizar ingeniería inversa de cualquier software contenido en el sitio web",
    useLicenseItem4: "Eliminar cualquier aviso de derechos de autor u otras notaciones propietarias de los materiales",
    serviceAvailabilityContent: "TypingTest se proporciona \"tal como está\" sin ninguna representación o garantía. No garantizamos que el servicio sea ininterrumpido, oportuno, seguro o libre de errores.",
    userConductContent: "Usted acepta usar TypingTest solo para propósitos legales y de una manera que no infrinja los derechos de otros o restrinja su uso y disfrute del servicio.",
    privacyContent: "Su privacidad es importante para nosotros. Por favor revise nuestra Política de Privacidad, que también gobierna su uso del servicio, para entender nuestras prácticas.",
    limitationsContent: "En ningún caso TypingTest o sus proveedores serán responsables por cualquier daño (incluyendo, sin limitación, daños por pérdida de datos o ganancias, o debido a interrupción del negocio) que surja del uso o la incapacidad de usar TypingTest.",
    modificationsContent: "TypingTest puede revisar estos términos de servicio en cualquier momento sin previo aviso. Al usar este servicio, usted acepta estar sujeto a la versión actual de estos términos de servicio.",
    contactInformationContent: "Si tiene alguna pregunta sobre estos Términos de Servicio, por favor contáctenos a través de nuestro sitio web.",
    privacyContent1: "Recopilamos información que nos proporciona directamente cuando utiliza nuestro servicio de prueba de mecanografía:",
    privacyList1: "Resultados de pruebas incluyendo palabras por minuto (PPM), precisión y errores",
    privacyList2: "Preferencias de idioma y dificultad",
    privacyList3: "Duración de la prueba y tiempos de finalización",
    howWeUseContent: "Utilizamos la información que recopilamos para:",
    useList1: "Proporcionar y mejorar nuestro servicio de prueba de mecanografía",
    useList2: "Mostrar sus resultados de prueba y progreso",
    useList3: "Analizar patrones de uso para mejorar la experiencia del usuario",
    useList4: "Generar estadísticas agregadas sobre el rendimiento de mecanografía",
    dataStorageContent: "Sus resultados de prueba se almacenan localmente en su navegador y temporalmente en nuestros servidores. No almacenamos permanentemente información de identificación personal a menos que usted la proporcione explícitamente.",
    thirdPartyContent: "Podemos usar servicios de análisis de terceros para entender cómo se usa nuestro servicio. Estos servicios pueden recopilar información sobre sus patrones de uso de acuerdo con sus propias políticas de privacidad.",
    dataSecurityContent: "Implementamos medidas técnicas y organizativas apropiadas para proteger su información contra acceso no autorizado, alteración, divulgación o destrucción.",
    contactUsContent: "Si tiene alguna pregunta sobre esta Política de Privacidad, contáctenos a través de nuestro sitio web."
  },
  fr: {
    appName: "Test de Frappe",
    testDuration: "Durée du test :",
    startTest: "Commencer le Test",
    testing: "Test en cours...",
    reset: "Réinitialiser",
    wpm: "MPM",
    accuracy: "Précision",
    timeLeft: "Temps Restant",
    errors: "Erreurs",
    typeTextAbove: "Tapez le texte ci-dessus",
    clickToStart: "Cliquez ici ou appuyez sur n'importe quelle touche pour commencer à taper",
    correct: "Correct",
    incorrect: "Incorrect",
    current: "Actuel",
    testComplete: "Test Terminé !",
    resultsDescription: "Voici les résultats de votre test de frappe",
    wordsPerMinute: "Mots Par Minute",
    average: "Moyenne",
    target: "Cible",
    timeTaken: "Temps Pris",
    testDurationLabel: "Durée du Test",
    totalErrors: "Erreurs Totales",
    incorrectKeystrokes: "Frappes incorrectes",
    takeAnotherTest: "Refaire un Test",
    shareResults: "Partager les Résultats",
    saveResults: "Sauvegarder les Résultats",
    saving: "Sauvegarde...",
    virtualKeyboard: "Clavier Virtuel",
    show: "Afficher",
    hide: "Masquer",
    footerText: "Améliorez vos compétences de frappe avec nos tests gratuits.",
    privacyPolicy: "Politique de Confidentialité",
    termsOfService: "Conditions d'Utilisation",
    backToApp: "Retour au Test de Frappe",
    lastUpdated: "Dernière mise à jour :",
    informationWeCollect: "Informations que Nous Collectons",
    howWeUseInfo: "Comment Nous Utilisons Vos Informations",
    dataStorage: "Stockage des Données",
    thirdPartyServices: "Services Tiers",
    dataSecurity: "Sécurité des Données",
    contactUs: "Nous Contacter",
    acceptanceOfTerms: "Acceptation des Conditions",
    useLicense: "Licence d'Utilisation",
    serviceAvailability: "Disponibilité du Service",
    userConduct: "Conduite de l'Utilisateur",
    privacy: "Confidentialité",
    limitations: "Limitations",
    modifications: "Modifications",
    contactInformation: "Informations de Contact",
    language: "Langue",
    difficulty: "Difficulté",
    beginner: "Débutant",
    intermediate: "Intermédiaire",
    advanced: "Avancé",
    // Terms of Service content
    acceptanceOfTermsContent: "En accédant et en utilisant TypingTest, vous acceptez et convenez d'être lié par les termes et dispositions de cet accord.",
    useLicenseContent: "La permission est accordée d'accéder temporairement à TypingTest pour un usage personnel et non commercial uniquement. Ceci est l'octroi d'une licence, non un transfert de titre, et sous cette licence vous ne pouvez pas :",
    useLicenseItem1: "Modifier ou copier les matériaux",
    useLicenseItem2: "Utiliser les matériaux à des fins commerciales ou pour tout affichage public",
    useLicenseItem3: "Tenter de faire de l'ingénierie inverse sur tout logiciel contenu sur le site web",
    useLicenseItem4: "Supprimer tout droit d'auteur ou autres notations propriétaires des matériaux",
    serviceAvailabilityContent: "TypingTest est fourni \"tel quel\" sans aucune représentation ou garantie. Nous ne garantissons pas que le service sera ininterrompu, opportun, sécurisé ou sans erreur.",
    userConductContent: "Vous acceptez d'utiliser TypingTest uniquement à des fins légales et d'une manière qui ne porte pas atteinte aux droits d'autrui ou ne restreint pas leur utilisation et jouissance du service.",
    privacyContent: "Votre vie privée est importante pour nous. Veuillez consulter notre Politique de Confidentialité, qui régit également votre utilisation du service, pour comprendre nos pratiques.",
    limitationsContent: "En aucun cas TypingTest ou ses fournisseurs ne seront responsables de tout dommage (y compris, sans limitation, les dommages pour perte de données ou de profit, ou dus à une interruption d'activité) découlant de l'utilisation ou de l'incapacité d'utiliser TypingTest.",
    modificationsContent: "TypingTest peut réviser ces conditions de service à tout moment sans préavis. En utilisant ce service, vous acceptez d'être lié par la version alors actuelle de ces conditions de service.",
    contactInformationContent: "Si vous avez des questions concernant ces Conditions de Service, veuillez nous contacter via notre site web.",
    privacyContent1: "Nous collectons les informations que vous nous fournissez directement lorsque vous utilisez notre service de test de frappe :",
    privacyList1: "Résultats des tests incluant les mots par minute (MPM), la précision et les erreurs",
    privacyList2: "Préférences de langue et de difficulté",
    privacyList3: "Durée des tests et temps de completion",
    howWeUseContent: "Nous utilisons les informations que nous collectons pour :",
    useList1: "Fournir et améliorer notre service de test de frappe",
    useList2: "Afficher vos résultats de test et vos progrès",
    useList3: "Analyser les modèles d'utilisation pour améliorer l'expérience utilisateur",
    useList4: "Générer des statistiques agrégées sur les performances de frappe",
    dataStorageContent: "Vos résultats de test sont stockés localement dans votre navigateur et temporairement sur nos serveurs. Nous ne stockons pas de manière permanente des informations d'identification personnelle à moins que vous ne les fournissiez explicitement.",
    thirdPartyContent: "Nous pouvons utiliser des services d'analyse tiers pour comprendre comment notre service est utilisé. Ces services peuvent collecter des informations sur vos modèles d'utilisation conformément à leurs propres politiques de confidentialité.",
    dataSecurityContent: "Nous mettons en place des mesures techniques et organisationnelles appropriées pour protéger vos informations contre l'accès non autorisé, l'altération, la divulgation ou la destruction.",
    contactUsContent: "Si vous avez des questions concernant cette Politique de Confidentialité, veuillez nous contacter via notre site web."
  }
};

export function getTranslation(language: string, key: keyof typeof translations.en): string {
  const lang = language as keyof typeof translations;
  return translations[lang]?.[key] || translations.en[key];
}

// Language configuration
export const SUPPORTED_LANGUAGES = ['en', 'zh', 'es', 'fr'] as const;
export const DEFAULT_LANGUAGE = 'en';
export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number];

export function isValidLanguage(lang: string): lang is SupportedLanguage {
  return SUPPORTED_LANGUAGES.includes(lang as SupportedLanguage);
}