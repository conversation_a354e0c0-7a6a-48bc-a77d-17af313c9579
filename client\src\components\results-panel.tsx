import { Trophy, Share2, Rotate<PERSON>cw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { getTranslation } from "@/lib/i18n";
import type { TypingTestState } from "@/hooks/use-typing-test";

interface ResultsPanelProps {
  testState: TypingTestState;
  language: string;
  difficulty: string;
  duration: number;
  onRetake: () => void;
}

export function ResultsPanel({
  testState,
  language,
  difficulty,
  duration,
  onRetake
}: ResultsPanelProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Force debug: Confirm component is using new code
  console.log('🔄 ResultsPanel rendered at:', new Date().toLocaleTimeString());
  console.log('🔄 Language prop:', language);
  console.log('🔄 Translation test (testComplete):', getTranslation(language, 'testComplete'));
  console.log('🔄 Translation test (resultsDescription):', getTranslation(language, 'resultsDescription'));
  console.log('🔄 Translation test (wordsPerMinute):', getTranslation(language, 'wordsPerMinute'));

  // Force language validation
  const validLanguage = language || 'en';
  console.log('🔄 Using language:', validLanguage);

  // Test all translations we'll use
  const translations = {
    testComplete: getTranslation(validLanguage, 'testComplete'),
    resultsDescription: getTranslation(validLanguage, 'resultsDescription'),
    wordsPerMinute: getTranslation(validLanguage, 'wordsPerMinute'),
    accuracy: getTranslation(validLanguage, 'accuracy'),
    timeTaken: getTranslation(validLanguage, 'timeTaken'),
    totalErrors: getTranslation(validLanguage, 'totalErrors'),
    takeAnotherTest: getTranslation(validLanguage, 'takeAnotherTest'),
    shareResults: getTranslation(validLanguage, 'shareResults'),
    saveResults: getTranslation(validLanguage, 'saveResults')
  };

  console.log('🔄 All translations:', translations);

  const saveResultMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', '/api/test-results', {
        wpm: testState.wpm,
        accuracy: Math.round(testState.accuracy * 100), // Store as integer for precision
        duration,
        errors: testState.errors,
        language,
        difficulty,
        completedAt: new Date().toISOString(),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/test-results'] });
      toast({
        title: getTranslation(validLanguage, 'resultsSaved'),
        description: getTranslation(validLanguage, 'resultsSavedDescription'),
      });
    },
    onError: () => {
      toast({
        title: getTranslation(validLanguage, 'failedToSaveResults'),
        description: getTranslation(validLanguage, 'failedToSaveDescription'),
        variant: "destructive",
      });
    },
  });

  const handleShare = async () => {
    const shareText = getTranslation(validLanguage, 'shareText')
      .replace('{wpm}', testState.wpm.toString())
      .replace('{accuracy}', testState.accuracy.toFixed(1));

    if (navigator.share) {
      try {
        await navigator.share({
          title: getTranslation(validLanguage, 'shareTitle'),
          text: shareText,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing or share failed
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(`${shareText} ${window.location.href}`);
        toast({
          title: getTranslation(validLanguage, 'copiedToClipboard'),
          description: getTranslation(validLanguage, 'copiedToClipboardDescription'),
        });
      } catch (error) {
        toast({
          title: getTranslation(validLanguage, 'shareFailed'),
          description: getTranslation(validLanguage, 'shareFailedDescription'),
          variant: "destructive",
        });
      }
    }
  };

  const averageWPM = 40; // This could be fetched from analytics
  const targetAccuracy = 95;

  return (
    <div
      key={`results-panel-${validLanguage}-${testState.wpm}-${testState.accuracy}`}
      className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8"
    >
      <div className="text-center mb-6 sm:mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full mb-4">
          <Trophy className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
        </div>
        <h2 className="text-xl sm:text-2xl font-bold text-slate-800 mb-2">{translations.testComplete}</h2>
        <p className="text-sm sm:text-base text-slate-600">{translations.resultsDescription}</p>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-blue-600 mb-1 sm:mb-2">{testState.wpm}</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{translations.wordsPerMinute}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(validLanguage, 'average')}: {averageWPM} {getTranslation(validLanguage, 'wpm')}</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-green-600 mb-1 sm:mb-2">{testState.accuracy.toFixed(1)}%</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{translations.accuracy}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(validLanguage, 'target')}: {targetAccuracy}%+</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-amber-600 mb-1 sm:mb-2">{duration}s</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{translations.timeTaken}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(validLanguage, 'testDurationLabel')}</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-red-600 mb-1 sm:mb-2">{testState.errors}</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">{translations.totalErrors}</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">{getTranslation(validLanguage, 'incorrectKeystrokes')}</div>
        </div>
      </div>

      <div className="border-t border-slate-200 pt-4 sm:pt-6">
        <div className="flex flex-col gap-3 sm:gap-4">
          <Button onClick={onRetake} className="w-full sm:w-auto px-6 py-3 min-h-[44px]">
            <RotateCcw className="w-4 h-4 mr-2" />
            {translations.takeAnotherTest}
          </Button>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Button variant="outline" onClick={handleShare} className="flex-1 sm:flex-none px-6 py-3 min-h-[44px]">
              <Share2 className="w-4 h-4 mr-2" />
              {translations.shareResults}
            </Button>
            <Button
              variant="outline"
              onClick={() => saveResultMutation.mutate()}
              disabled={saveResultMutation.isPending}
              className="flex-1 sm:flex-none px-6 py-3 min-h-[44px]"
            >
              {saveResultMutation.isPending ? getTranslation(validLanguage, 'saving') : translations.saveResults}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
