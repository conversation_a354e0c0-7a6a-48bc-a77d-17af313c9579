import { Trophy, Share2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { TypingTestState } from "@/hooks/use-typing-test";

interface ResultsPanelProps {
  testState: TypingTestState;
  language: string;
  difficulty: string;
  duration: number;
  onRetake: () => void;
}

export function ResultsPanel({
  testState,
  language,
  difficulty,
  duration,
  onRetake
}: ResultsPanelProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const saveResultMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', '/api/test-results', {
        wpm: testState.wpm,
        accuracy: Math.round(testState.accuracy * 100), // Store as integer for precision
        duration,
        errors: testState.errors,
        language,
        difficulty,
        completedAt: new Date().toISOString(),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/test-results'] });
      toast({
        title: "Results saved!",
        description: "Your test results have been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Failed to save results",
        description: "There was an error saving your test results.",
        variant: "destructive",
      });
    },
  });

  const handleShare = async () => {
    const shareText = `I just scored ${testState.wpm} WPM with ${testState.accuracy.toFixed(1)}% accuracy on TypingMaster!`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'My Typing Test Results',
          text: shareText,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing or share failed
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(`${shareText} ${window.location.href}`);
        toast({
          title: "Copied to clipboard!",
          description: "Your results have been copied to the clipboard.",
        });
      } catch (error) {
        toast({
          title: "Share failed",
          description: "Unable to share your results.",
          variant: "destructive",
        });
      }
    }
  };

  const averageWPM = 40; // This could be fetched from analytics
  const targetAccuracy = 95;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
      <div className="text-center mb-6 sm:mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full mb-4">
          <Trophy className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
        </div>
        <h2 className="text-xl sm:text-2xl font-bold text-slate-800 mb-2">Test Complete!</h2>
        <p className="text-sm sm:text-base text-slate-600">Here are your typing test results</p>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-blue-600 mb-1 sm:mb-2">{testState.wpm}</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">Words Per Minute</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">Average: {averageWPM} WPM</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-green-600 mb-1 sm:mb-2">{testState.accuracy.toFixed(1)}%</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">Accuracy</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">Target: {targetAccuracy}%+</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-amber-600 mb-1 sm:mb-2">{duration}s</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">Time Taken</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">Test Duration</div>
        </div>

        <div className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-red-600 mb-1 sm:mb-2">{testState.errors}</div>
          <div className="text-xs sm:text-sm font-medium text-slate-600">Total Errors</div>
          <div className="text-xs text-slate-500 mt-1 hidden sm:block">Incorrect keystrokes</div>
        </div>
      </div>

      <div className="border-t border-slate-200 pt-4 sm:pt-6">
        <div className="flex flex-col gap-3 sm:gap-4">
          <Button onClick={onRetake} className="w-full sm:w-auto px-6 py-3 min-h-[44px]">
            <RotateCcw className="w-4 h-4 mr-2" />
            Take Another Test
          </Button>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Button variant="outline" onClick={handleShare} className="flex-1 sm:flex-none px-6 py-3 min-h-[44px]">
              <Share2 className="w-4 h-4 mr-2" />
              Share Results
            </Button>
            <Button
              variant="outline"
              onClick={() => saveResultMutation.mutate()}
              disabled={saveResultMutation.isPending}
              className="flex-1 sm:flex-none px-6 py-3 min-h-[44px]"
            >
              {saveResultMutation.isPending ? "Saving..." : "Save Results"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
