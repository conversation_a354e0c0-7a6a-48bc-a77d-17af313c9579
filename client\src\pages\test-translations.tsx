import { useState } from 'react';
import { getTranslation } from '@/lib/i18n';
import { useLanguage } from '@/contexts/LanguageContext';
import { ResultsPanel } from '@/components/results-panel';

export default function TestTranslations() {
  const { language, setLanguage } = useLanguage();
  const [showResults, setShowResults] = useState(false);

  // Mock test state for testing
  const mockTestState = {
    wpm: 45,
    accuracy: 92.5,
    errors: 8,
    timeRemaining: 0,
    testText: '',
    currentPosition: 0,
    userInput: '',
    isCompleted: true
  };

  const testKeys = [
    'testComplete',
    'resultsDescription', 
    'wordsPerMinute',
    'accuracy',
    'timeTaken',
    'totalErrors',
    'takeAnotherTest',
    'shareResults',
    'saveResults'
  ];

  return (
    <div className="min-h-screen bg-slate-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">翻译测试页面</h1>
        
        {/* Language Selector */}
        <div className="mb-8">
          <label className="block text-sm font-medium mb-2">选择语言:</label>
          <select 
            value={language} 
            onChange={(e) => setLanguage(e.target.value as any)}
            className="border rounded px-3 py-2"
          >
            <option value="en">English</option>
            <option value="zh">中文</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
          </select>
        </div>

        {/* Translation Test */}
        <div className="bg-white rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">翻译键值测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testKeys.map(key => (
              <div key={key} className="border rounded p-3">
                <div className="text-sm text-gray-600 mb-1">{key}:</div>
                <div className="font-medium">{getTranslation(language, key as any)}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Results Panel Test */}
        <div className="mb-8">
          <button 
            onClick={() => setShowResults(!showResults)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            {showResults ? '隐藏' : '显示'} 结果面板
          </button>
        </div>

        {showResults && (
          <div>
            <h2 className="text-xl font-bold mb-4">结果面板测试</h2>
            <ResultsPanel
              testState={mockTestState}
              language={language}
              difficulty="intermediate"
              duration={60}
              onRetake={() => console.log('Retake clicked')}
            />
          </div>
        )}

        {/* Debug Info */}
        <div className="bg-gray-100 rounded-lg p-4">
          <h3 className="font-bold mb-2">调试信息:</h3>
          <div>当前语言: {language}</div>
          <div>测试翻译: {getTranslation(language, 'testComplete')}</div>
          <div>时间戳: {new Date().toLocaleTimeString()}</div>
        </div>
      </div>
    </div>
  );
}
