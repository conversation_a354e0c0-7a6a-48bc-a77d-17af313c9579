// 测试结果页面国际化修复验证脚本
// 在浏览器控制台中运行此脚本

(function() {
  console.log('🌍 开始测试结果页面国际化修复验证...\n');
  
  const results = {
    passed: 0,
    failed: 0,
    warnings: 0
  };
  
  function logResult(status, message) {
    const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${icon} ${message}`);
    results[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
  }
  
  // 检测当前语言
  const currentPath = window.location.pathname;
  const languageMatch = currentPath.match(/^\/([a-z]{2})\//);
  const currentLanguage = languageMatch ? languageMatch[1] : 'en';
  
  console.log(`1️⃣ 检测到当前语言: ${currentLanguage}`);
  
  // 语言名称映射
  const languageNames = {
    'en': 'English',
    'zh': '中文',
    'es': 'Español', 
    'fr': 'Français'
  };
  
  console.log(`   语言名称: ${languageNames[currentLanguage] || '未知'}\n`);
  
  // 检查是否存在结果面板
  console.log('2️⃣ 检查结果面板是否存在...');
  const resultsPanel = document.querySelector('[class*="bg-white rounded-xl shadow-sm border border-slate-200"]');
  
  if (resultsPanel) {
    logResult('pass', '找到结果面板');
    
    // 检查标题
    console.log('\n3️⃣ 检查标题翻译...');
    const title = resultsPanel.querySelector('h2');
    if (title) {
      const titleText = title.textContent.trim();
      console.log(`   标题文本: "${titleText}"`);
      
      const expectedTitles = {
        'en': 'Test Complete!',
        'zh': '测试完成！',
        'es': '¡Prueba Completada!',
        'fr': 'Test Terminé !'
      };
      
      if (titleText === expectedTitles[currentLanguage]) {
        logResult('pass', `标题正确翻译为 ${currentLanguage} 语言`);
      } else {
        logResult('fail', `标题翻译错误。期望: "${expectedTitles[currentLanguage]}", 实际: "${titleText}"`);
      }
    } else {
      logResult('fail', '未找到标题元素');
    }
    
    // 检查描述文本
    console.log('\n4️⃣ 检查描述文本翻译...');
    const description = resultsPanel.querySelector('p');
    if (description) {
      const descText = description.textContent.trim();
      console.log(`   描述文本: "${descText}"`);
      
      const expectedDescriptions = {
        'en': 'Here are your typing test results',
        'zh': '这是您的打字测试结果',
        'es': 'Aquí están los resultados de tu prueba de mecanografía',
        'fr': 'Voici les résultats de votre test de frappe'
      };
      
      if (descText === expectedDescriptions[currentLanguage]) {
        logResult('pass', `描述文本正确翻译为 ${currentLanguage} 语言`);
      } else {
        logResult('fail', `描述文本翻译错误。期望: "${expectedDescriptions[currentLanguage]}", 实际: "${descText}"`);
      }
    } else {
      logResult('fail', '未找到描述文本元素');
    }
    
    // 检查统计数据标签
    console.log('\n5️⃣ 检查统计数据标签翻译...');
    const statLabels = resultsPanel.querySelectorAll('.text-xs.font-medium, .text-sm.font-medium');
    
    const expectedLabels = {
      'en': ['Words Per Minute', 'Accuracy', 'Time Taken', 'Total Errors'],
      'zh': ['每分钟字数', '准确率', '用时', '总错误数'],
      'es': ['Palabras Por Minuto', 'Precisión', 'Tiempo Tomado', 'Errores Totales'],
      'fr': ['Mots Par Minute', 'Précision', 'Temps Pris', 'Erreurs Totales']
    };
    
    const currentLabels = Array.from(statLabels).map(label => label.textContent.trim());
    console.log(`   找到的标签: ${JSON.stringify(currentLabels)}`);
    console.log(`   期望的标签: ${JSON.stringify(expectedLabels[currentLanguage])}`);
    
    let labelsCorrect = true;
    expectedLabels[currentLanguage].forEach((expectedLabel, index) => {
      if (currentLabels.includes(expectedLabel)) {
        logResult('pass', `统计标签 "${expectedLabel}" 翻译正确`);
      } else {
        logResult('fail', `统计标签 "${expectedLabel}" 翻译错误或缺失`);
        labelsCorrect = false;
      }
    });
    
    // 检查按钮文本
    console.log('\n6️⃣ 检查按钮文本翻译...');
    const buttons = resultsPanel.querySelectorAll('button');
    
    const expectedButtons = {
      'en': ['Take Another Test', 'Share Results', 'Save Results'],
      'zh': ['再次测试', '分享结果', '保存结果'],
      'es': ['Tomar Otra Prueba', 'Compartir Resultados', 'Guardar Resultados'],
      'fr': ['Passer un Autre Test', 'Partager les Résultats', 'Sauvegarder les Résultats']
    };
    
    const currentButtons = Array.from(buttons).map(btn => btn.textContent.trim());
    console.log(`   找到的按钮: ${JSON.stringify(currentButtons)}`);
    console.log(`   期望的按钮: ${JSON.stringify(expectedButtons[currentLanguage])}`);
    
    expectedButtons[currentLanguage].forEach((expectedButton, index) => {
      if (currentButtons.some(btn => btn.includes(expectedButton))) {
        logResult('pass', `按钮 "${expectedButton}" 翻译正确`);
      } else {
        logResult('fail', `按钮 "${expectedButton}" 翻译错误或缺失`);
      }
    });
    
  } else {
    logResult('warning', '未找到结果面板 - 请先完成一次打字测试');
    console.log('\n📝 请按以下步骤操作：');
    console.log('1. 点击"开始测试"按钮');
    console.log('2. 完成打字测试');
    console.log('3. 重新运行此脚本验证结果');
  }
  
  // 显示总结
  console.log('\n📊 验证结果总结:');
  console.log('==================');
  console.log(`✅ 通过: ${results.passed}`);
  console.log(`❌ 失败: ${results.failed}`);
  console.log(`⚠️ 警告: ${results.warnings}`);
  
  if (results.failed === 0 && results.passed > 0) {
    console.log('\n🎉 结果页面国际化修复验证通过！');
  } else if (results.failed > 0) {
    console.log('\n🔧 发现问题，需要进一步检查');
  } else {
    console.log('\n⏳ 需要完成打字测试后再次验证');
  }
  
  console.log('\n🔄 测试其他语言：');
  console.log('- 中文: http://localhost:5000/zh');
  console.log('- 法语: http://localhost:5000/fr');
  console.log('- 西班牙语: http://localhost:5000/es');
  console.log('- 英语: http://localhost:5000/en');
  
})();
