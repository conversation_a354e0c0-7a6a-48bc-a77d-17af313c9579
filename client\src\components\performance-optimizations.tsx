import { Suspense, lazy } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ResultsPanel } from '@/components/results-panel';
import { VirtualKeyboard } from '@/components/virtual-keyboard';

// Temporarily disable lazy loading to fix caching issues
// const LazyResultsPanel = lazy(() =>
//   import('@/components/results-panel').then(module => ({ default: module.ResultsPanel }))
// );

// const LazyVirtualKeyboard = lazy(() =>
//   import('@/components/virtual-keyboard').then(module => ({ default: module.VirtualKeyboard }))
// );

// Loading fallbacks
const ResultsPanelSkeleton = () => (
  <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
    <div className="text-center mb-6 sm:mb-8">
      <Skeleton className="w-12 h-12 sm:w-16 sm:h-16 rounded-full mx-auto mb-4" />
      <Skeleton className="h-6 sm:h-8 w-48 mx-auto mb-2" />
      <Skeleton className="h-4 w-64 mx-auto" />
    </div>
    
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="text-center p-3 sm:p-4 lg:p-6 bg-slate-50 rounded-lg">
          <Skeleton className="h-8 sm:h-10 lg:h-12 w-16 mx-auto mb-2" />
          <Skeleton className="h-4 w-20 mx-auto mb-1" />
          <Skeleton className="h-3 w-16 mx-auto" />
        </div>
      ))}
    </div>
    
    <div className="border-t border-slate-200 pt-4 sm:pt-6">
      <div className="flex flex-col gap-3 sm:gap-4">
        <Skeleton className="h-12 w-full sm:w-48 mx-auto" />
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
          <Skeleton className="h-12 w-full sm:w-32" />
          <Skeleton className="h-12 w-full sm:w-32" />
        </div>
      </div>
    </div>
  </div>
);

const VirtualKeyboardSkeleton = () => (
  <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6">
    <div className="flex items-center justify-between mb-4">
      <Skeleton className="h-6 w-32" />
      <Skeleton className="h-8 w-16" />
    </div>
    
    <div className="space-y-1 sm:space-y-2">
      {Array.from({ length: 5 }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex justify-center space-x-1">
          {Array.from({ length: rowIndex === 4 ? 1 : 12 }).map((_, keyIndex) => (
            <Skeleton 
              key={keyIndex} 
              className={`h-10 ${rowIndex === 4 ? 'w-32 sm:w-48 lg:w-64' : 'w-8 sm:w-10'}`} 
            />
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Optimized components with lazy loading
interface OptimizedResultsPanelProps {
  testState: any;
  language: string;
  difficulty: string;
  duration: number;
  onRetake: () => void;
}

export const OptimizedResultsPanel = (props: OptimizedResultsPanelProps) => (
  // Temporarily use direct import to fix caching issues
  <ResultsPanel {...props} />
);

interface OptimizedVirtualKeyboardProps {
  isVisible: boolean;
  onToggleVisibility: () => void;
  currentChar?: string;
  language: string;
  onKeyPress: (key: string) => void;
}

export const OptimizedVirtualKeyboard = (props: OptimizedVirtualKeyboardProps) => (
  // Temporarily use direct import to fix caching issues
  <VirtualKeyboard {...props} />
);

// Performance monitoring hook
export const usePerformanceMonitoring = () => {
  const measurePerformance = (name: string, fn: () => void) => {
    const start = performance.now();
    fn();
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${name} took ${end - start} milliseconds`);
    }
  };

  const measureAsync = async (name: string, fn: () => Promise<void>) => {
    const start = performance.now();
    await fn();
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${name} took ${end - start} milliseconds`);
    }
  };

  return { measurePerformance, measureAsync };
};

// Image optimization component
interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
}

export const OptimizedImage = ({ src, alt, className, width, height }: OptimizedImageProps) => {
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      loading="lazy"
      decoding="async"
      style={{
        contentVisibility: 'auto',
        containIntrinsicSize: width && height ? `${width}px ${height}px` : 'auto'
      }}
    />
  );
};

// Preload critical resources
export const preloadCriticalResources = () => {
  // Preload fonts
  const fontLink = document.createElement('link');
  fontLink.rel = 'preload';
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap';
  fontLink.as = 'style';
  document.head.appendChild(fontLink);

  // Preload critical API endpoints
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then(() => {
      // Cache critical API endpoints
      fetch('/api/text-samples/en/intermediate').catch(() => {});
    });
  }
};
