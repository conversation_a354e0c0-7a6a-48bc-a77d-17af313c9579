// 调试语言上下文脚本
// 在浏览器控制台中运行此脚本来检查语言状态

(function() {
  console.log('🔍 调试语言上下文状态...\n');
  
  // 检查当前URL
  console.log('1️⃣ URL信息:');
  console.log(`   当前URL: ${window.location.href}`);
  console.log(`   路径: ${window.location.pathname}`);
  console.log(`   查询参数: ${window.location.search}`);
  
  // 检查语言提取
  const path = window.location.pathname;
  const languageMatch = path.match(/^\/([a-z]{2})\//);
  const extractedLanguage = languageMatch ? languageMatch[1] : 'en';
  console.log(`   提取的语言: ${extractedLanguage}\n`);
  
  // 检查localStorage
  console.log('2️⃣ 存储信息:');
  try {
    const storedLang = localStorage.getItem('preferred-language');
    console.log(`   localStorage语言: ${storedLang}`);
  } catch (e) {
    console.log(`   localStorage错误: ${e.message}`);
  }
  
  // 检查浏览器语言
  console.log(`   浏览器语言: ${navigator.language}`);
  console.log(`   浏览器语言(简化): ${navigator.language.split('-')[0]}\n`);
  
  // 检查React组件状态
  console.log('3️⃣ React组件状态:');
  
  // 查找语言选择器
  const languageSelector = document.querySelector('select[value]');
  if (languageSelector) {
    console.log(`   语言选择器值: ${languageSelector.value}`);
  } else {
    console.log('   未找到语言选择器');
  }
  
  // 查找标题元素
  const appTitle = document.querySelector('h1 span');
  if (appTitle) {
    console.log(`   应用标题: "${appTitle.textContent}"`);
  }
  
  // 检查结果面板
  console.log('\n4️⃣ 结果面板检查:');
  const resultsPanel = document.querySelector('[class*="bg-white rounded-xl shadow-sm border border-slate-200"]');
  
  if (resultsPanel) {
    console.log('   ✅ 找到结果面板');
    
    // 检查标题
    const title = resultsPanel.querySelector('h2');
    if (title) {
      console.log(`   标题: "${title.textContent}"`);
    }
    
    // 检查描述
    const description = resultsPanel.querySelector('p');
    if (description) {
      console.log(`   描述: "${description.textContent}"`);
    }
    
    // 检查按钮
    const buttons = resultsPanel.querySelectorAll('button');
    console.log(`   按钮数量: ${buttons.length}`);
    buttons.forEach((btn, index) => {
      console.log(`   按钮${index + 1}: "${btn.textContent.trim()}"`);
    });
    
  } else {
    console.log('   ❌ 未找到结果面板');
    console.log('   提示: 请先完成一次打字测试');
  }
  
  // 检查翻译函数
  console.log('\n5️⃣ 翻译函数测试:');
  
  // 尝试访问全局的翻译函数（如果存在）
  if (window.getTranslation) {
    console.log('   ✅ 找到全局翻译函数');
    try {
      const testTranslation = window.getTranslation(extractedLanguage, 'testComplete');
      console.log(`   测试翻译 (${extractedLanguage}): "${testTranslation}"`);
    } catch (e) {
      console.log(`   翻译函数错误: ${e.message}`);
    }
  } else {
    console.log('   ❌ 未找到全局翻译函数');
  }
  
  // 检查React DevTools
  console.log('\n6️⃣ React DevTools信息:');
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('   ✅ React DevTools已安装');
    console.log('   提示: 可以在React DevTools中检查LanguageContext的值');
  } else {
    console.log('   ❌ React DevTools未安装');
  }
  
  // 提供调试建议
  console.log('\n📋 调试建议:');
  console.log('1. 检查浏览器控制台是否有错误信息');
  console.log('2. 清除浏览器缓存并刷新页面');
  console.log('3. 检查网络标签中的API请求');
  console.log('4. 使用React DevTools检查组件状态');
  console.log('5. 确认服务器正在运行并返回正确的数据');
  
  // 提供快速测试链接
  console.log('\n🔗 快速测试链接:');
  console.log('- 中文: http://localhost:5000/zh');
  console.log('- 法语: http://localhost:5000/fr');
  console.log('- 西班牙语: http://localhost:5000/es');
  console.log('- 英语: http://localhost:5000/en');
  
})();
